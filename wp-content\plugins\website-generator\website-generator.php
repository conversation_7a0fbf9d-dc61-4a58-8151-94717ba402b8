<?php
/**
 * Plugin Name: RepairLift WP Customizer
 * Plugin URI: https://repairlift.com
 * Description: A comprehensive website customizer for repair shops and local businesses with visual editing capabilities, multi-page management, and professional design system.
 * Version: 2.0.0
 * Author: RepairLift Team
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}



class WebsiteGeneratorPro {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_ajax_generate_website', array($this, 'handle_form_submission'));
        add_action('wp_ajax_create_backup', array($this, 'handle_create_backup'));
        add_action('wp_ajax_restore_backup', array($this, 'handle_restore_backup'));
        add_action('wp_ajax_get_text_content', array($this, 'handle_get_text_content'));
        add_action('wp_ajax_update_text_content', array($this, 'handle_update_text_content'));
        add_action('wp_ajax_get_unused_assets', array($this, 'handle_get_unused_assets'));
        add_action('wp_ajax_get_current_content', array($this, 'handle_get_current_content'));
        add_action('wp_ajax_apply_visual_changes', array($this, 'handle_apply_visual_changes'));
        add_action('wp_ajax_upload_logo', array($this, 'handle_upload_logo'));
        add_action('wp_ajax_upload_logo_enhanced', array($this, 'handle_upload_logo_enhanced'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        register_activation_hook(__FILE__, array($this, 'activate'));
    }
    
    public function init() {
        // Load dependencies
        require_once plugin_dir_path(__FILE__) . 'includes/theme-customizer.php';
        require_once plugin_dir_path(__FILE__) . 'includes/content-manager.php';
        require_once plugin_dir_path(__FILE__) . 'includes/text-manager.php';

        // Initialize text replacement and design styles on frontend
        if (!is_admin()) {
            $text_manager = new TextManager();
            $text_manager->apply_custom_texts();

            // Inject button text color styles
            $theme_customizer = new ThemeCustomizer();
            add_action('wp_head', array($theme_customizer, 'inject_button_text_styles'));
        }
    }
    
    public function add_admin_menu() {
        add_menu_page(
            'RepairLift WP Customizer',
            'RepairLift Customizer',
            'manage_options',
            'website-generator',
            array($this, 'admin_page'),
            'dashicons-admin-site-alt3',
            30
        );
    }
    
    public function admin_page() {
        // Include the admin page file
        include_once plugin_dir_path(__FILE__) . 'admin/admin-page.php';
    }
    
    public function enqueue_scripts($hook) {
        if ($hook !== 'toplevel_page_website-generator') {
            return;
        }
        
        wp_enqueue_script(
            'website-generator-js',
            plugin_dir_url(__FILE__) . 'admin/admin-scripts.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        wp_enqueue_style(
            'website-generator-css',
            plugin_dir_url(__FILE__) . 'admin/admin-styles.css',
            array(),
            '1.0.0'
        );
        
        wp_localize_script('website-generator-js', 'ajax_object', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('website_generator_nonce')
        ));
    }
    
    public function handle_form_submission() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'website_generator_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }
        
        $customizer = new ThemeCustomizer();
        $content_manager = new ContentManager();
        
        $result = array();
        
        try {
            // Update colors
            if (isset($_POST['primary_color']) && isset($_POST['secondary_color'])) {
                $result['colors'] = $customizer->update_colors(
                    $_POST['primary_color'],
                    $_POST['secondary_color']
                );
            }
            
            // Handle logo upload
            if (isset($_FILES['logo'])) {
                $result['logo'] = $customizer->update_logo($_FILES['logo']);
            }
            
            // Update site identity
            if (isset($_POST['company_name'])) {
                $result['identity'] = $content_manager->update_site_identity(
                    $_POST['company_name'],
                    $_POST['tagline'] ?? ''
                );
            }
            
            // Update content
            if (isset($_POST['homepage_content'])) {
                $result['content'] = $content_manager->update_homepage_content($_POST);
            }
            
            wp_send_json_success($result);
            
        } catch (Exception $e) {
            wp_send_json_error('Error: ' . $e->getMessage());
        }
    }
    
    public function handle_create_backup() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $content_manager = new ContentManager();
        $result = $content_manager->create_backup();

        wp_send_json($result);
    }

    public function handle_restore_backup() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $backup_id = sanitize_text_field($_POST['backup_id']);
        $content_manager = new ContentManager();
        $result = $content_manager->restore_backup($backup_id);

        wp_send_json($result);
    }

    public function handle_get_text_content() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $text_manager = new TextManager();
        $content = $text_manager->get_all_text_content();

        wp_send_json_success($content);
    }

    public function handle_update_text_content() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $updates = $_POST['updates'];
        $text_manager = new TextManager();
        $result = $text_manager->update_text_content($updates);

        wp_send_json($result);
    }

    public function handle_get_unused_assets() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $text_manager = new TextManager();
        $unused_assets = $text_manager->get_unused_assets();

        wp_send_json_success($unused_assets);
    }

    /**
     * Handle get current content AJAX request
     */
    public function handle_get_current_content() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $current_content = $this->get_current_content();

        wp_send_json_success($current_content);
    }

    /**
     * Handle apply visual changes AJAX request
     */
    public function handle_apply_visual_changes() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $blocks = $_POST['blocks'] ?? array();
        $result = $this->apply_visual_changes($blocks);

        wp_send_json_success($result);
    }

    /**
     * Handle enhanced logo upload AJAX request with WebP conversion
     */
    public function handle_upload_logo_enhanced() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        if (!isset($_FILES['logo_upload'])) {
            wp_send_json_error('No file uploaded');
        }

        $result = $this->process_logo_upload_enhanced($_FILES['logo_upload']);

        if ($result['status'] === 'success') {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * Handle logo upload AJAX request
     */
    public function handle_upload_logo() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        if (!isset($_FILES['logo_upload'])) {
            wp_send_json_error('No file uploaded');
        }

        $result = $this->process_logo_upload($_FILES['logo_upload']);

        if ($result['status'] === 'success') {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * Get current content from homepage
     */
    private function get_current_content() {
        $homepage_id = get_option('page_on_front');

        if (!$homepage_id) {
            return array('error' => 'No homepage set');
        }

        $homepage = get_post($homepage_id);
        if (!$homepage) {
            return array('error' => 'Homepage not found');
        }

        // Parse current content and extract text
        $content = $homepage->post_content;

        // Extract hero section content
        $hero = array(
            'heading' => $this->extract_text_between($content, '<h1', '</h1>'),
            'tagline' => $this->extract_tagline($content),
            'button1' => 'Start a Repair',
            'button2' => 'Call Us Now'
        );

        // Extract other sections
        $onestop = array(
            'heading' => 'Your One-Stop Shop Repair Store',
            'description' => $this->extract_onestop_description($content),
            'button' => 'Our Location'
        );

        $contact = array(
            'phone' => $this->extract_phone($content),
            'email' => $this->extract_email($content),
            'address' => $this->extract_address($content)
        );

        // Get current design system settings
        $design = $this->get_current_design_system();

        // Get current logo information
        $logo = $this->get_current_logo();

        return array(
            'hero' => $hero,
            'onestop' => $onestop,
            'contact' => $contact,
            'design' => $design,
            'logo' => $logo
        );
    }

    /**
     * Apply visual changes to content
     */
    private function apply_visual_changes($blocks) {
        $content_manager = new ContentManager();

        // Convert blocks data to format expected by content manager
        $data = array();

        if (isset($blocks['hero'])) {
            $data['company_name'] = $blocks['hero']['heading'] ?? '';
            $data['tagline'] = $blocks['hero']['tagline'] ?? '';
        }

        if (isset($blocks['contact'])) {
            $data['phone'] = $blocks['contact']['phone'] ?? '';
            $data['email'] = $blocks['contact']['email'] ?? '';
            $data['address'] = $blocks['contact']['address'] ?? '';
        }

        if (isset($blocks['onestop'])) {
            $data['about_text'] = $blocks['onestop']['description'] ?? '';
        }

        // Handle design system changes
        if (isset($blocks['design'])) {
            $this->update_design_system($blocks['design']);
        }

        // Apply changes using existing content manager
        return $content_manager->update_homepage_content($data);
    }

    /**
     * Helper function to extract text between HTML tags
     */
    private function extract_text_between($content, $start_tag, $end_tag) {
        $pattern = '/' . preg_quote($start_tag, '/') . '[^>]*>(.*?)' . preg_quote($end_tag, '/') . '/s';
        if (preg_match($pattern, $content, $matches)) {
            return strip_tags($matches[1]);
        }
        return '';
    }

    /**
     * Extract tagline from content
     */
    private function extract_tagline($content) {
        // Look for the specific tagline pattern
        if (strpos($content, 'Smartphones | Tablets | Computers') !== false) {
            return 'Smartphones | Tablets | Computers | & More';
        }
        return '';
    }

    /**
     * Extract one-stop shop description
     */
    private function extract_onestop_description($content) {
        if (strpos($content, 'Same Day Service') !== false) {
            return 'Same Day Service on Major Brands & Devices. Visit us at a location near you. We take pride in what we do. And what we do best is restore your device back to its original condition. You can rest easy when you have your device repaired with us. We offer a warranty on all our repairs.';
        }
        return '';
    }

    /**
     * Extract phone number from content
     */
    private function extract_phone($content) {
        if (preg_match('/\(555\) 222-1111/', $content)) {
            return '(*************';
        }
        return '';
    }

    /**
     * Extract email from content
     */
    private function extract_email($content) {
        if (preg_match('/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/', $content, $matches)) {
            return $matches[0];
        }
        return '';
    }

    /**
     * Extract address from content
     */
    private function extract_address($content) {
        if (strpos($content, '123 Main Street') !== false) {
            return '123 Main Street, Anytown, FL 12345';
        }
        return '';
    }

    /**
     * Get current design system settings
     */
    private function get_current_design_system() {
        // Get theme.json content
        $theme_json_path = get_stylesheet_directory() . '/theme.json';

        if (!file_exists($theme_json_path)) {
            return $this->get_default_design_system();
        }

        $theme_json = json_decode(file_get_contents($theme_json_path), true);

        if (!$theme_json || !isset($theme_json['settings']['color']['palette'])) {
            return $this->get_default_design_system();
        }

        $palette = $theme_json['settings']['color']['palette'];
        $colors = array();

        foreach ($palette as $color) {
            $colors[$color['slug']] = $color['color'];
        }

        return array(
            'primary_color' => $colors['primary'] ?? '#165C9C',
            'secondary_color' => $colors['secondary'] ?? '#345C00',
            'accent_color' => $colors['tertiary'] ?? '#9DFF20',
            'button_style' => get_option('website_generator_button_style', 'pill'),
            'button_size' => get_option('website_generator_button_size', 'medium')
        );
    }

    /**
     * Get default design system settings
     */
    private function get_default_design_system() {
        return array(
            'primary_color' => '#165C9C',
            'secondary_color' => '#345C00',
            'accent_color' => '#9DFF20',
            'button_style' => 'pill',
            'button_size' => 'medium'
        );
    }

    /**
     * Update design system settings
     */
    private function update_design_system($design_data) {
        // Update theme.json
        $this->update_theme_json_colors($design_data);

        // Save design options including text colors
        $design_options = array(
            'primary_color' => $design_data['primary_color'] ?? '#9DFF20',
            'secondary_color' => $design_data['secondary_color'] ?? '#345C00',
            'background_color' => $design_data['background_color'] ?? '#F6F6F6',
            'primary_text_color' => $design_data['primary_text_color'] ?? '#FFFFFF',
            'secondary_text_color' => $design_data['secondary_text_color'] ?? '#FFFFFF',
            'button_style' => $design_data['button_style'] ?? 'square',
            'button_size' => $design_data['button_size'] ?? 'medium'
        );

        update_option('website_generator_design_options', $design_options);

        return array(
            'status' => 'success',
            'message' => 'Design system updated successfully'
        );
    }

    /**
     * Update theme.json color palette
     */
    private function update_theme_json_colors($design_data) {
        $theme_json_path = get_stylesheet_directory() . '/theme.json';

        if (!file_exists($theme_json_path)) {
            return false;
        }

        $theme_json = json_decode(file_get_contents($theme_json_path), true);

        if (!$theme_json) {
            return false;
        }

        // Update color palette
        if (isset($theme_json['settings']['color']['palette'])) {
            foreach ($theme_json['settings']['color']['palette'] as &$color) {
                switch ($color['slug']) {
                    case 'primary':
                        $color['color'] = $design_data['primary_color'] ?? $color['color'];
                        break;
                    case 'secondary':
                        $color['color'] = $design_data['secondary_color'] ?? $color['color'];
                        break;
                    case 'tertiary':
                        $color['color'] = $design_data['background_color'] ?? $color['color'];
                        break;
                }
            }
        }

        // Write updated theme.json
        $json_content = json_encode($theme_json, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
        return file_put_contents($theme_json_path, $json_content);
    }

    /**
     * Convert image to WebP format
     */
    private function convert_to_webp($source_file, $quality = 80) {
        // Check if GD supports WebP
        if (!function_exists('imagewebp')) {
            return false;
        }

        $image_info = getimagesize($source_file);
        if (!$image_info) {
            return false;
        }

        $mime_type = $image_info['mime'];

        // Create image resource based on type
        switch ($mime_type) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($source_file);
                break;
            case 'image/png':
                $image = imagecreatefrompng($source_file);
                // Preserve transparency for PNG
                imagealphablending($image, false);
                imagesavealpha($image, true);
                break;
            default:
                return false;
        }

        if (!$image) {
            return false;
        }

        // Generate WebP filename
        $path_info = pathinfo($source_file);
        $webp_file = $path_info['dirname'] . '/' . $path_info['filename'] . '.webp';

        // Convert to WebP
        $success = imagewebp($image, $webp_file, $quality);
        imagedestroy($image);

        if ($success && file_exists($webp_file)) {
            $original_size = filesize($source_file);
            $webp_size = filesize($webp_file);

            return array(
                'webp_file' => $webp_file,
                'webp_url' => str_replace(wp_upload_dir()['basedir'], wp_upload_dir()['baseurl'], $webp_file),
                'original_size' => $original_size,
                'webp_size' => $webp_size,
                'compression_ratio' => $original_size > 0 ? round((1 - $webp_size / $original_size) * 100, 1) : 0
            );
        }

        return false;
    }

    /**
     * Enhanced logo upload processing with WebP conversion
     */
    private function process_logo_upload_enhanced($file) {
        // Original upload processing
        $result = $this->process_logo_upload($file);

        if ($result['status'] === 'success') {
            // Attempt WebP conversion for JPG/PNG
            if (in_array($file['type'], ['image/jpeg', 'image/png'])) {
                $original_file = get_attached_file($result['attachment_id']);
                $webp_result = $this->convert_to_webp($original_file);

                if ($webp_result) {
                    // Create WebP attachment
                    $webp_attachment = array(
                        'post_mime_type' => 'image/webp',
                        'post_title' => 'Company Logo (WebP) - ' . date('Y-m-d H:i:s'),
                        'post_content' => '',
                        'post_status' => 'inherit',
                        'post_parent' => $result['attachment_id'] // Link to original
                    );

                    $webp_attach_id = wp_insert_attachment($webp_attachment, $webp_result['webp_file']);

                    if (!is_wp_error($webp_attach_id)) {
                        // Generate attachment metadata for WebP
                        if (!function_exists('wp_generate_attachment_metadata')) {
                            require_once(ABSPATH . 'wp-admin/includes/image.php');
                        }

                        $webp_attach_data = wp_generate_attachment_metadata($webp_attach_id, $webp_result['webp_file']);
                        wp_update_attachment_metadata($webp_attach_id, $webp_attach_data);

                        $result['webp'] = array(
                            'attachment_id' => $webp_attach_id,
                            'url' => $webp_result['webp_url'],
                            'file_size' => $webp_result['webp_size'],
                            'compression_saved' => $webp_result['compression_ratio'],
                            'original_size' => $webp_result['original_size']
                        );

                        $result['message'] .= ' WebP version created with ' . $webp_result['compression_ratio'] . '% size reduction.';
                    }
                }
            }
        }

        return $result;
    }

    /**
     * Process logo upload
     */
    private function process_logo_upload($file) {
        // Validate file
        $allowed_types = array('image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp');
        if (!in_array($file['type'], $allowed_types)) {
            return array(
                'status' => 'error',
                'message' => 'Invalid file type. Please upload PNG, JPG, SVG, or WebP.'
            );
        }

        // Check file size (5MB limit)
        if ($file['size'] > 5 * 1024 * 1024) {
            return array(
                'status' => 'error',
                'message' => 'File size must be less than 5MB.'
            );
        }

        // Handle upload
        if (!function_exists('wp_handle_upload')) {
            require_once(ABSPATH . 'wp-admin/includes/file.php');
        }

        $uploaded_file = wp_handle_upload($file, array('test_form' => false));

        if (isset($uploaded_file['error'])) {
            return array(
                'status' => 'error',
                'message' => 'Upload failed: ' . $uploaded_file['error']
            );
        }

        // Create attachment
        $attachment = array(
            'post_mime_type' => $uploaded_file['type'],
            'post_title' => 'Company Logo - ' . date('Y-m-d H:i:s'),
            'post_content' => '',
            'post_status' => 'inherit'
        );

        $attach_id = wp_insert_attachment($attachment, $uploaded_file['file']);

        if (is_wp_error($attach_id)) {
            return array(
                'status' => 'error',
                'message' => 'Failed to create attachment'
            );
        }

        // Generate attachment metadata
        if (!function_exists('wp_generate_attachment_metadata')) {
            require_once(ABSPATH . 'wp-admin/includes/image.php');
        }

        $attach_data = wp_generate_attachment_metadata($attach_id, $uploaded_file['file']);
        wp_update_attachment_metadata($attach_id, $attach_data);

        // Set as custom logo
        set_theme_mod('custom_logo', $attach_id);

        // Get image dimensions
        $image_info = getimagesize($uploaded_file['file']);

        return array(
            'status' => 'success',
            'message' => 'Logo uploaded successfully',
            'attachment_id' => $attach_id,
            'url' => $uploaded_file['url'],
            'width' => $image_info[0] ?? 0,
            'height' => $image_info[1] ?? 0,
            'file_size' => $file['size']
        );
    }

    /**
     * Get current logo information
     */
    private function get_current_logo() {
        $logo_id = get_theme_mod('custom_logo');

        if (!$logo_id) {
            return array(
                'has_logo' => false,
                'url' => '',
                'width' => 0,
                'height' => 0,
                'filename' => '',
                'file_size' => 0
            );
        }

        $logo_url = wp_get_attachment_image_url($logo_id, 'full');
        $logo_meta = wp_get_attachment_metadata($logo_id);
        $logo_post = get_post($logo_id);

        return array(
            'has_logo' => true,
            'attachment_id' => $logo_id,
            'url' => $logo_url,
            'width' => $logo_meta['width'] ?? 0,
            'height' => $logo_meta['height'] ?? 0,
            'filename' => basename($logo_url),
            'file_size' => filesize(get_attached_file($logo_id)) ?: 0
        );
    }

    public function activate() {
        // Create necessary database tables or options
        add_option('website_generator_version', '1.0.0');
        add_option('website_generator_backups', array());
        add_option('website_generator_custom_texts', array());
    }
}



// Initialize the plugin
new WebsiteGeneratorPro();
