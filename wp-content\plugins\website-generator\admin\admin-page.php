<?php
/**
 * Admin page for Website Generator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1>RepairLift WP Customizer</h1>
    <p>Customize your repair shop website with our comprehensive visual editor, multi-page management, and professional design system.</p>
    
    <!-- Navigation Tabs -->
    <div class="nav-tab-wrapper">
        <a href="#" class="nav-tab nav-tab-active" data-tab="homepage">🏠 Homepage</a>
        <a href="#" class="nav-tab" data-tab="iphone-repair">📱 iPhone Repair</a>
        <a href="#" class="nav-tab" data-tab="android-repair">🤖 Android Repair</a>
        <a href="#" class="nav-tab" data-tab="tablet-repair">📱 Tablet Repair</a>
        <a href="#" class="nav-tab" data-tab="computer-repair">💻 Computer Repair</a>
        <a href="#" class="nav-tab" data-tab="watch-repair">⌚ Apple Watch</a>
        <a href="#" class="nav-tab" data-tab="console-repair">🎮 Game Console</a>
        <a href="#" class="nav-tab" data-tab="global-settings">🌐 Global Settings</a>
        <a href="#" class="nav-tab" data-tab="advanced">🔧 Advanced Tools</a>
    </div>

    <div id="website-generator-container">

        <!-- Homepage Tab -->
        <div id="homepage-tab" class="tab-content active">
            <div class="visual-editor-header">
                <h2>Visual Block Editor</h2>
                <p>Edit each section of your website and see live preview changes</p>
                <div class="editor-controls">
                    <button type="button" id="load-current-content" class="button button-secondary">
                        <span class="dashicons dashicons-download"></span>
                        Load Current Content
                    </button>
                    <button type="button" id="preview-all-changes" class="button button-primary">
                        <span class="dashicons dashicons-visibility"></span>
                        Preview All Changes
                    </button>
                    <button type="button" id="apply-all-changes" class="button button-primary">
                        <span class="dashicons dashicons-yes"></span>
                        Apply All Changes
                    </button>
                </div>
            </div>

            <div class="visual-blocks-container">
                <!-- Hero Section Block -->
                <div class="visual-block" data-block="hero">
                    <div class="block-header">
                        <h3><span class="dashicons dashicons-format-image"></span> Hero Section</h3>
                        <div class="block-status">
                            <span class="status-indicator" data-status="unchanged">Unchanged</span>
                        </div>
                    </div>
                    <div class="block-content">
                        <div class="block-editor">
                            <div class="form-row">
                                <label for="hero_heading">Main Heading:</label>
                                <input type="text" id="hero_heading" name="hero_heading" class="block-input"
                                       value="Premier Device Repair in Anytown, FL" data-preview="hero-heading">
                                <p class="field-help">Edit this text to customize your main heading</p>
                            </div>
                            <div class="form-row">
                                <label for="hero_tagline">Tagline:</label>
                                <input type="text" id="hero_tagline" name="hero_tagline" class="block-input"
                                       value="Smartphones | Tablets | Computers | & More" data-preview="hero-tagline">
                                <p class="field-help">Describe the devices you repair</p>
                            </div>
                            <div class="form-row">
                                <label for="hero_button1">Button 1 Text:</label>
                                <input type="text" id="hero_button1" name="hero_button1" class="block-input"
                                       value="Start a Repair" data-preview="hero-button1">
                                <p class="field-help">Primary call-to-action button</p>
                            </div>
                            <div class="form-row">
                                <label for="hero_button2">Button 2 Text:</label>
                                <input type="text" id="hero_button2" name="hero_button2" class="block-input"
                                       value="Call Us Now" data-preview="hero-button2">
                                <p class="field-help">Secondary call-to-action button</p>
                            </div>
                        </div>
                        <div class="block-preview">
                            <div class="preview-container hero-preview">
                                <h1 id="preview-hero-heading">Premier Device Repair in Anytown, FL</h1>
                                <p id="preview-hero-tagline">Smartphones | Tablets | Computers | & More</p>
                                <div class="preview-buttons">
                                    <button class="preview-btn primary" id="preview-hero-button1">Start a Repair</button>
                                    <button class="preview-btn secondary" id="preview-hero-button2">Call Us Now</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- One-Stop Shop Section Block -->
                <div class="visual-block" data-block="onestop">
                    <div class="block-header">
                        <h3><span class="dashicons dashicons-store"></span> One-Stop Shop Section</h3>
                        <div class="block-status">
                            <span class="status-indicator" data-status="unchanged">Unchanged</span>
                        </div>
                    </div>
                    <div class="block-content">
                        <div class="block-editor">
                            <div class="form-row">
                                <label for="onestop_heading">Section Heading:</label>
                                <input type="text" id="onestop_heading" name="onestop_heading" class="block-input"
                                       value="Your One-Stop Shop Repair Store" data-preview="onestop-heading">
                                <p class="field-help">Main title for your services section</p>
                            </div>
                            <div class="form-row">
                                <label for="onestop_description">Description:</label>
                                <textarea id="onestop_description" name="onestop_description" class="block-input" rows="3"
                                          data-preview="onestop-description">Same Day Service on Major Brands & Devices. We repair smartphones, tablets, computers, and more with professional quality and fast turnaround times.</textarea>
                                <p class="field-help">Describe your repair services and capabilities</p>
                            </div>
                            <div class="form-row">
                                <label for="onestop_button">Button Text:</label>
                                <input type="text" id="onestop_button" name="onestop_button" class="block-input"
                                       value="Our Location" data-preview="onestop-button">
                                <p class="field-help">Button to show your store location</p>
                            </div>
                        </div>
                        <div class="block-preview">
                            <div class="preview-container onestop-preview">
                                <h3 id="preview-onestop-heading">Your One-Stop Shop Repair Store</h3>
                                <p id="preview-onestop-description">Same Day Service on Major Brands & Devices. Visit us at a location near you. We take pride in what we do.</p>
                                <button class="preview-btn primary" id="preview-onestop-button">Our Location</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Buy Devices Section Block -->
                <div class="visual-block" data-block="buydevices">
                    <div class="block-header">
                        <h3><span class="dashicons dashicons-cart"></span> Buy Devices Section</h3>
                        <div class="block-status">
                            <span class="status-indicator" data-status="unchanged">Unchanged</span>
                        </div>
                    </div>
                    <div class="block-content">
                        <div class="block-editor">
                            <div class="form-row">
                                <label for="buy_heading">Section Heading:</label>
                                <input type="text" id="buy_heading" name="buy_heading" class="block-input"
                                       value="Buy Certified Pre-Owned Devices" data-preview="buy-heading">
                                <p class="field-help">Title for device sales section</p>
                            </div>
                            <div class="form-row">
                                <label for="buy_description">Description:</label>
                                <textarea id="buy_description" name="buy_description" class="block-input" rows="3"
                                          data-preview="buy-description">Our inventory changes rapidly and moves quickly. We have a variety of refurbished devices available at competitive prices with warranty included.</textarea>
                                <p class="field-help">Describe your device sales and inventory</p>
                            </div>
                            <div class="form-row">
                                <label for="buy_button">Button Text:</label>
                                <input type="text" id="buy_button" name="buy_button" class="block-input"
                                       value="Buy a Device" data-preview="buy-button">
                                <p class="field-help">Button for device purchasing</p>
                            </div>
                        </div>
                        <div class="block-preview">
                            <div class="preview-container buy-preview">
                                <h4 id="preview-buy-heading">Buy Certified Pre-Owned Devices</h4>
                                <p id="preview-buy-description">Our inventory changes rapidly and moves quickly because of our low prices.</p>
                                <button class="preview-btn primary" id="preview-buy-button">Buy a Device</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sell Devices Section Block -->
                <div class="visual-block" data-block="selldevices">
                    <div class="block-header">
                        <h3><span class="dashicons dashicons-money"></span> Sell Devices Section</h3>
                        <div class="block-status">
                            <span class="status-indicator" data-status="unchanged">Unchanged</span>
                        </div>
                    </div>
                    <div class="block-content">
                        <div class="block-editor">
                            <div class="form-row">
                                <label for="sell_heading">Section Heading:</label>
                                <input type="text" id="sell_heading" name="sell_heading" class="block-input"
                                       value="Sell Your Used Or Broken Devices" data-preview="sell-heading">
                                <p class="field-help">Title for device buyback section</p>
                            </div>
                            <div class="form-row">
                                <label for="sell_description">Description:</label>
                                <textarea id="sell_description" name="sell_description" class="block-input" rows="3"
                                          data-preview="sell-description">We pay you for your devices same day. Get instant quotes and fair market value for your smartphones, tablets, and other electronics.</textarea>
                                <p class="field-help">Describe your device buyback program</p>
                            </div>
                            <div class="form-row">
                                <label for="sell_button">Button Text:</label>
                                <input type="text" id="sell_button" name="sell_button" class="block-input"
                                       value="Sell a Device" data-preview="sell-button">
                                <p class="field-help">Button for device selling</p>
                            </div>
                        </div>
                        <div class="block-preview">
                            <div class="preview-container sell-preview">
                                <h5 id="preview-sell-heading">Sell Your Used Or Broken Devices</h5>
                                <p id="preview-sell-description">We pay you for your devices same day, just come by our store.</p>
                                <button class="preview-btn primary" id="preview-sell-button">Sell a Device</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- CTA Section Block -->
                <div class="visual-block" data-block="cta">
                    <div class="block-header">
                        <h3><span class="dashicons dashicons-megaphone"></span> Call-to-Action Section</h3>
                        <div class="block-status">
                            <span class="status-indicator" data-status="unchanged">Unchanged</span>
                        </div>
                    </div>
                    <div class="block-content">
                        <div class="block-editor">
                            <div class="form-row">
                                <label for="cta_heading">CTA Heading:</label>
                                <input type="text" id="cta_heading" name="cta_heading" class="block-input"
                                       value="Repair Your Device Today!" data-preview="cta-heading">
                                <p class="field-help">Final call-to-action heading</p>
                            </div>
                            <div class="form-row">
                                <label for="cta_description">Description:</label>
                                <textarea id="cta_description" name="cta_description" class="block-input" rows="3"
                                          data-preview="cta-description">We offer reliable and professional service with fast turnaround times. Get your device back to working condition quickly and affordably.</textarea>
                                <p class="field-help">Encourage visitors to take action</p>
                            </div>
                            <div class="form-row">
                                <label for="cta_button">Button Text:</label>
                                <input type="text" id="cta_button" name="cta_button" class="block-input"
                                       value="Get Instant Quote" data-preview="cta-button">
                                <p class="field-help">Final call-to-action button</p>
                            </div>
                        </div>
                        <div class="block-preview">
                            <div class="preview-container cta-preview">
                                <h2 id="preview-cta-heading">Repair Your Device Today!</h2>
                                <p id="preview-cta-description">We offer reliable and professional service for your electronic device.</p>
                                <button class="preview-btn primary" id="preview-cta-button">Get Instant Quote</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information Block -->
                <div class="visual-block" data-block="contact">
                    <div class="block-header">
                        <h3><span class="dashicons dashicons-phone"></span> Contact Information</h3>
                        <div class="block-status">
                            <span class="status-indicator" data-status="unchanged">Unchanged</span>
                        </div>
                    </div>
                    <div class="block-content">
                        <div class="block-editor">
                            <div class="form-row">
                                <label for="contact_phone">Phone Number:</label>
                                <input type="tel" id="contact_phone" name="contact_phone" class="block-input"
                                       value="(*************" data-preview="contact-phone">
                                <p class="field-help">Your business phone number</p>
                            </div>
                            <div class="form-row">
                                <label for="contact_email">Email Address:</label>
                                <input type="email" id="contact_email" name="contact_email" class="block-input"
                                       value="<EMAIL>" data-preview="contact-email">
                                <p class="field-help">Your business email address</p>
                            </div>
                            <div class="form-row">
                                <label for="contact_address">Business Address:</label>
                                <textarea id="contact_address" name="contact_address" class="block-input" rows="2"
                                          data-preview="contact-address">123 Main Street, Anytown, FL 12345</textarea>
                                <p class="field-help">Your business address</p>
                            </div>
                        </div>
                        <div class="block-preview">
                            <div class="preview-container contact-preview">
                                <p><strong>Visit Our Store</strong></p>
                                <p id="preview-contact-phone">(*************</p>
                                <p id="preview-contact-email"><EMAIL></p>
                                <p id="preview-contact-address">123 Main Street, Anytown, FL 12345</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Logo & Branding Block -->
                <div class="visual-block" data-block="logo">
                    <div class="block-header">
                        <h3><span class="dashicons dashicons-format-image"></span> Logo & Branding</h3>
                        <div class="block-status">
                            <span class="status-indicator" data-status="unchanged">Unchanged</span>
                        </div>
                    </div>
                    <div class="block-content">
                        <div class="block-editor">
                            <div class="form-section">
                                <h4>Logo Upload</h4>
                                <div class="form-row">
                                    <label for="logo_upload">Upload New Logo:</label>
                                    <div class="logo-upload-container">
                                        <input type="file" id="logo_upload" name="logo_upload" class="block-input"
                                               accept="image/png,image/jpeg,image/jpg,image/svg+xml,image/webp"
                                               data-preview="logo">
                                        <div class="upload-help">
                                            <p>Supported: PNG, JPG, SVG, WebP | Max: 5MB | Min: 100x100px</p>
                                            <p class="webp-info">💡 JPG/PNG files will be automatically converted to WebP for better performance</p>
                                        </div>
                                    </div>
                                    <div id="upload-results" class="upload-results-container" style="display: none;"></div>
                                </div>

                                <div class="form-row" id="logo-editing-tools" style="display: none;">
                                    <label>Logo Editing Tools:</label>
                                    <div class="editing-toolbar">
                                        <button type="button" class="edit-btn" data-tool="crop">
                                            <span class="dashicons dashicons-image-crop"></span> Crop
                                        </button>
                                        <button type="button" class="edit-btn" data-tool="resize">
                                            <span class="dashicons dashicons-image-resize"></span> Resize
                                        </button>
                                        <button type="button" class="edit-btn" data-tool="adjust">
                                            <span class="dashicons dashicons-admin-appearance"></span> Adjust
                                        </button>
                                        <button type="button" class="edit-btn" data-tool="filter">
                                            <span class="dashicons dashicons-art"></span> Filters
                                        </button>
                                    </div>
                                </div>

                                <!-- Crop Controls -->
                                <div class="editing-panel" id="crop-panel" style="display: none;">
                                    <h5>Crop Logo</h5>
                                    <div class="crop-controls">
                                        <label for="aspect_ratio">Aspect Ratio:</label>
                                        <select id="aspect_ratio" class="block-input">
                                            <option value="free">Free Form</option>
                                            <option value="1:1">Square (1:1)</option>
                                            <option value="16:9">Wide (16:9)</option>
                                            <option value="4:3">Standard (4:3)</option>
                                            <option value="3:2">Photo (3:2)</option>
                                        </select>
                                        <button type="button" class="apply-btn" data-action="apply-crop">Apply Crop</button>
                                    </div>
                                </div>

                                <!-- Resize Controls -->
                                <div class="editing-panel" id="resize-panel" style="display: none;">
                                    <h5>Resize Logo</h5>
                                    <div class="resize-controls">
                                        <div class="size-inputs">
                                            <label for="logo_width">Width:</label>
                                            <input type="number" id="logo_width" class="block-input" min="50" max="2000" value="200">
                                            <label for="logo_height">Height:</label>
                                            <input type="number" id="logo_height" class="block-input" min="50" max="2000" value="200">
                                            <label>
                                                <input type="checkbox" id="maintain_aspect" checked> Maintain Aspect Ratio
                                            </label>
                                        </div>
                                        <div class="size-presets">
                                            <button type="button" class="preset-btn" data-size="32,32">Favicon</button>
                                            <button type="button" class="preset-btn" data-size="180,180">Mobile</button>
                                            <button type="button" class="preset-btn" data-size="200,60">Header</button>
                                            <button type="button" class="preset-btn" data-size="400,400">Social</button>
                                        </div>
                                        <button type="button" class="apply-btn" data-action="apply-resize">Apply Resize</button>
                                    </div>
                                </div>

                                <!-- Adjustment Controls -->
                                <div class="editing-panel" id="adjust-panel" style="display: none;">
                                    <h5>Adjust Logo</h5>
                                    <div class="adjustment-controls">
                                        <div class="slider-group">
                                            <label for="brightness">Brightness:</label>
                                            <input type="range" id="brightness" min="-100" max="100" value="0" class="adjustment-slider">
                                            <span class="slider-value">0</span>
                                        </div>
                                        <div class="slider-group">
                                            <label for="contrast">Contrast:</label>
                                            <input type="range" id="contrast" min="-100" max="100" value="0" class="adjustment-slider">
                                            <span class="slider-value">0</span>
                                        </div>
                                        <div class="slider-group">
                                            <label for="saturation">Saturation:</label>
                                            <input type="range" id="saturation" min="-100" max="100" value="0" class="adjustment-slider">
                                            <span class="slider-value">0</span>
                                        </div>
                                        <div class="slider-group">
                                            <label for="hue">Hue:</label>
                                            <input type="range" id="hue" min="0" max="360" value="0" class="adjustment-slider">
                                            <span class="slider-value">0°</span>
                                        </div>
                                        <button type="button" class="apply-btn" data-action="apply-adjustments">Apply Adjustments</button>
                                        <button type="button" class="reset-btn" data-action="reset-adjustments">Reset</button>
                                    </div>
                                </div>

                                <!-- Filter Controls -->
                                <div class="editing-panel" id="filter-panel" style="display: none;">
                                    <h5>Apply Filters</h5>
                                    <div class="filter-controls">
                                        <div class="filter-grid">
                                            <button type="button" class="filter-btn" data-filter="none">None</button>
                                            <button type="button" class="filter-btn" data-filter="grayscale">Grayscale</button>
                                            <button type="button" class="filter-btn" data-filter="sepia">Sepia</button>
                                            <button type="button" class="filter-btn" data-filter="high-contrast">High Contrast</button>
                                            <button type="button" class="filter-btn" data-filter="vintage">Vintage</button>
                                            <button type="button" class="filter-btn" data-filter="modern">Modern</button>
                                        </div>
                                        <button type="button" class="apply-btn" data-action="apply-filter">Apply Filter</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="block-preview">
                            <div class="preview-container logo-preview">
                                <h4>Logo Preview</h4>
                                <div class="logo-preview-area">
                                    <div class="preview-logo-container">
                                        <img id="preview-logo" src="" alt="Logo Preview" style="display: none;">
                                        <div class="logo-placeholder">
                                            <span class="dashicons dashicons-format-image"></span>
                                            <p>Upload a logo to see preview</p>
                                        </div>
                                    </div>
                                    <div class="logo-info">
                                        <p><strong>Current Logo:</strong> <span id="logo-filename">None</span></p>
                                        <p><strong>Dimensions:</strong> <span id="logo-dimensions">-</span></p>
                                        <p><strong>File Size:</strong> <span id="logo-filesize">-</span></p>
                                    </div>
                                </div>

                                <!-- Canvas for editing -->
                                <canvas id="logo-edit-canvas" style="display: none; max-width: 100%; border: 1px solid #ddd;"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Design System Block -->
                <div class="visual-block" data-block="design">
                    <div class="block-header">
                        <h3><span class="dashicons dashicons-art"></span> RepairLift Design System</h3>
                        <div class="block-status">
                            <span class="status-indicator" data-status="unchanged">Unchanged</span>
                        </div>
                    </div>
                    <div class="block-content">
                        <div class="block-editor">
                            <div class="form-section">
                                <h4>Brand Colors & Live Preview</h4>
                                <p class="section-description">Customize colors and see instant preview of your repair business website</p>

                                <div class="color-design-container">
                                    <div class="color-controls">
                                        <div class="form-row">
                                            <label for="primary_color">Primary Brand Color:</label>
                                            <div class="color-input-group">
                                                <input type="color" id="primary_color" name="primary_color" class="block-input color-input"
                                                       value="#9DFF20" data-preview="primary-color">
                                                <span class="color-value">#9DFF20</span>
                                                <div class="color-swatch">
                                                    <div class="swatch" id="preview-primary-color" style="background: var(--wp--preset--color--primary, #9DFF20);"></div>
                                                </div>
                                            </div>
                                            <p class="description">Main buttons: "Start Repair", "Get Quote", "Call Now"</p>
                                        </div>

                                        <div class="form-row">
                                            <label for="secondary_color">Secondary Brand Color:</label>
                                            <div class="color-input-group">
                                                <input type="color" id="secondary_color" name="secondary_color" class="block-input color-input"
                                                       value="#345C00" data-preview="secondary-color">
                                                <span class="color-value">#345C00</span>
                                                <div class="color-swatch">
                                                    <div class="swatch" id="preview-secondary-color" style="background: var(--wp--preset--color--secondary, #345C00);"></div>
                                                </div>
                                            </div>
                                            <p class="description">Button hover states, links, section accents</p>
                                        </div>

                                        <div class="form-row">
                                            <label for="background_color">Section Background:</label>
                                            <div class="color-input-group">
                                                <input type="color" id="background_color" name="background_color" class="block-input color-input"
                                                       value="#F6F6F6" data-preview="background-color">
                                                <span class="color-value">#F6F6F6</span>
                                                <div class="color-swatch">
                                                    <div class="swatch" id="preview-background-color" style="background: var(--wp--preset--color--tertiary, #F6F6F6);"></div>
                                                </div>
                                            </div>
                                            <p class="description">Feature cards, about section backgrounds</p>
                                        </div>

                                        <h5>Button Text Colors</h5>
                                        <p class="section-description">Choose text colors for each button type to match your design</p>

                                        <div class="form-row">
                                            <label for="primary_text_color">Primary Button Text:</label>
                                            <div class="color-input-group">
                                                <input type="color" id="primary_text_color" name="primary_text_color" class="block-input color-input"
                                                       value="#FFFFFF" data-preview="primary-text-color">
                                                <span class="color-value">#FFFFFF</span>
                                                <div class="color-swatch">
                                                    <div class="swatch" id="preview-primary-text-color" style="background: #FFFFFF; border: 2px solid #ddd;"></div>
                                                </div>
                                            </div>
                                            <p class="description">Text color for "Start Repair", "Get Quote" buttons</p>
                                        </div>

                                        <div class="form-row">
                                            <label for="secondary_text_color">Secondary Button Text:</label>
                                            <div class="color-input-group">
                                                <input type="color" id="secondary_text_color" name="secondary_text_color" class="block-input color-input"
                                                       value="#FFFFFF" data-preview="secondary-text-color">
                                                <span class="color-value">#FFFFFF</span>
                                                <div class="color-swatch">
                                                    <div class="swatch" id="preview-secondary-text-color" style="background: #FFFFFF; border: 2px solid #ddd;"></div>
                                                </div>
                                            </div>
                                            <p class="description">Text color for "Call Us Now", secondary buttons</p>
                                        </div>
                                    </div>

                                    <div class="live-preview">
                                        <h5>Live Website Preview</h5>
                                        <div class="template-preview" id="template-preview">
                                            <div class="preview-section hero-section">
                                                <h3 class="preview-heading">Premier Device Repair in Your City</h3>
                                                <p class="preview-tagline">Smartphones | Tablets | Computers | & More</p>
                                                <div class="preview-buttons">
                                                    <button class="preview-btn primary" id="preview-primary-btn">Start a Repair</button>
                                                    <button class="preview-btn secondary" id="preview-secondary-btn">Call Us Now</button>
                                                </div>
                                            </div>

                                            <div class="preview-section about-section">
                                                <h4 class="preview-heading">Your One-Stop Shop Repair Store</h4>
                                                <p class="preview-body">Same Day Service on Major Brands & Devices</p>
                                                <button class="preview-btn primary">Our Location</button>
                                            </div>
                                        </div>

                                        <div class="color-legend">
                                            <div class="legend-item">
                                                <span class="legend-color primary-demo"></span>
                                                <span>Primary Button</span>
                                            </div>
                                            <div class="legend-item">
                                                <span class="legend-color secondary-demo"></span>
                                                <span>Secondary Button</span>
                                            </div>
                                            <div class="legend-item">
                                                <span class="legend-color background-demo"></span>
                                                <span>Background</span>
                                            </div>
                                            <div class="legend-item">
                                                <span class="legend-color primary-text-demo"></span>
                                                <span>Primary Text</span>
                                            </div>
                                            <div class="legend-item">
                                                <span class="legend-color secondary-text-demo"></span>
                                                <span>Secondary Text</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <h4>Button Styling</h4>
                                <p class="section-description">Customize the appearance of action buttons throughout your site</p>

                                <div class="form-row">
                                    <label for="button_style">Button Shape:</label>
                                    <select id="button_style" name="button_style" class="block-input" data-preview="button-style">
                                        <option value="square" selected>Square (Professional)</option>
                                        <option value="rounded">Rounded (Modern)</option>
                                        <option value="pill">Pill (Friendly)</option>
                                    </select>
                                    <p class="description">Current theme uses square buttons for professional appearance</p>
                                </div>

                                <div class="form-row">
                                    <label for="button_size">Button Size:</label>
                                    <select id="button_size" name="button_size" class="block-input" data-preview="button-size">
                                        <option value="small">Small (Compact)</option>
                                        <option value="medium" selected>Medium (Standard)</option>
                                        <option value="large">Large (Prominent)</option>
                                    </select>
                                    <p class="description">Affects all CTA buttons: "Start Repair", "Get Quote", etc.</p>
                                </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <h4>Text & Typography</h4>
                                <p class="section-description">Customize text appearance for better readability and brand consistency</p>

                                <div class="form-row">
                                    <label for="heading_weight">Heading Weight:</label>
                                    <select id="heading_weight" name="heading_weight" class="block-input" data-preview="heading-weight">
                                        <option value="normal">Normal (400)</option>
                                        <option value="medium">Medium (500)</option>
                                        <option value="semibold" selected>Semi-Bold (600)</option>
                                        <option value="bold">Bold (700)</option>
                                    </select>
                                    <p class="description">Weight for main headings: "Premier Device Repair", section titles</p>
                                </div>

                                <div class="form-row">
                                    <label for="text_contrast">Text Contrast:</label>
                                    <select id="text_contrast" name="text_contrast" class="block-input" data-preview="text-contrast">
                                        <option value="standard" selected>Standard (Black on White)</option>
                                        <option value="high">High Contrast (Enhanced)</option>
                                        <option value="soft">Soft (Dark Gray on White)</option>
                                    </select>
                                    <p class="description">Affects readability of body text and descriptions</p>
                                </div>
                            </div>


                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <!-- iPhone Repair Tab -->
        <div id="iphone-repair-tab" class="tab-content">
            <div class="service-page-editor">
                <div class="page-header">
                    <h2>📱 iPhone Repair Page Editor</h2>
                    <p>Edit content specific to the iPhone repair service page</p>
                    <div class="page-controls">
                        <button type="button" id="load-iphone-content" class="button button-secondary">
                            <span class="dashicons dashicons-download"></span>
                            Load Current Content
                        </button>
                        <button type="button" id="preview-iphone-page" class="button button-secondary">
                            <span class="dashicons dashicons-visibility"></span>
                            Preview Page
                        </button>
                        <button type="button" id="save-iphone-content" class="button button-primary">
                            <span class="dashicons dashicons-yes"></span>
                            Save Changes
                        </button>
                    </div>
                </div>

                <div class="service-content-blocks">
                    <!-- Hero Section -->
                    <div class="content-block">
                        <h3><span class="dashicons dashicons-format-image"></span> Hero Section</h3>
                        <div class="block-fields">
                            <div class="form-row">
                                <label for="iphone_page_title">Page Title:</label>
                                <input type="text" id="iphone_page_title" name="iphone_page_title" class="block-input"
                                       value="iPhone Repairs">
                                <p class="field-help">Main title for the iPhone repair page</p>
                            </div>
                            <div class="form-row">
                                <label for="iphone_hero_description">Hero Description:</label>
                                <textarea id="iphone_hero_description" name="iphone_hero_description" class="block-input" rows="4">We understand when your iPhone needs repair, you need it fixed fast and done right. Our certified technicians specialize in iPhone repairs with genuine parts and warranty coverage.</textarea>
                                <p class="field-help">Describe why customers should choose you for iPhone repairs</p>
                            </div>
                            <div class="form-row">
                                <label for="iphone_cta_button">CTA Button Text:</label>
                                <input type="text" id="iphone_cta_button" name="iphone_cta_button" class="block-input"
                                       value="Select Your Model to Get a Quote">
                                <p class="field-help">Button text for iPhone repair quotes</p>
                            </div>
                        </div>
                    </div>

                    <!-- Common Repairs Section -->
                    <div class="content-block">
                        <h3><span class="dashicons dashicons-admin-tools"></span> Common Repairs Section</h3>
                        <div class="block-fields">
                            <div class="form-row">
                                <label for="iphone_repairs_title">Section Title:</label>
                                <input type="text" id="iphone_repairs_title" name="iphone_repairs_title" class="block-input"
                                       value="iPhone Common Repairs">
                                <p class="field-help">Title for the iPhone repairs services section</p>
                            </div>
                            <div class="form-row">
                                <label for="iphone_repairs_description">Section Description:</label>
                                <textarea id="iphone_repairs_description" name="iphone_repairs_description" class="block-input" rows="2">Our dedicated team of repair experts can fix any iPhone issue quickly and professionally. From cracked screens to battery replacements, we've got you covered.</textarea>
                                <p class="field-help">Describe your iPhone repair services and expertise</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Android Repair Tab -->
        <div id="android-repair-tab" class="tab-content">
            <div class="service-page-editor">
                <div class="page-header">
                    <h2>🤖 Android Repair Page Editor</h2>
                    <p>Edit content specific to the Android repair service page</p>
                    <div class="page-controls">
                        <button type="button" id="load-android-content" class="button button-secondary">
                            <span class="dashicons dashicons-download"></span>
                            Load Current Content
                        </button>
                        <button type="button" id="preview-android-page" class="button button-secondary">
                            <span class="dashicons dashicons-visibility"></span>
                            Preview Page
                        </button>
                        <button type="button" id="save-android-content" class="button button-primary">
                            <span class="dashicons dashicons-yes"></span>
                            Save Changes
                        </button>
                    </div>
                </div>

                <div class="service-content-blocks">
                    <!-- Hero Section -->
                    <div class="content-block">
                        <h3><span class="dashicons dashicons-format-image"></span> Hero Section</h3>
                        <div class="block-fields">
                            <div class="form-row">
                                <label for="android_page_title">Page Title:</label>
                                <input type="text" id="android_page_title" name="android_page_title" class="block-input"
                                       value="Android Repairs">
                                <p class="field-help">Main title for the Android repair page</p>
                            </div>
                            <div class="form-row">
                                <label for="android_hero_description">Hero Description:</label>
                                <textarea id="android_hero_description" name="android_hero_description" class="block-input" rows="4">We understand when your Android phone needs repair, you need it fixed fast and done right. Our experienced technicians work on all Android brands with quality parts and reliable service.</textarea>
                                <p class="field-help">Describe why customers should choose you for Android repairs</p>
                            </div>
                            <div class="form-row">
                                <label for="android_cta_button">CTA Button Text:</label>
                                <input type="text" id="android_cta_button" name="android_cta_button" class="block-input"
                                       value="Select Your Model to Get a Quote">
                                <p class="field-help">Button text for Android repair quotes</p>
                            </div>
                        </div>
                    </div>

                    <!-- Common Repairs Section -->
                    <div class="content-block">
                        <h3><span class="dashicons dashicons-admin-tools"></span> Common Repairs Section</h3>
                        <div class="block-fields">
                            <div class="form-row">
                                <label for="android_repairs_title">Section Title:</label>
                                <input type="text" id="android_repairs_title" name="android_repairs_title" class="block-input"
                                       value="Android Common Repairs">
                                <p class="field-help">Title for the Android repairs services section</p>
                            </div>
                            <div class="form-row">
                                <label for="android_repairs_description">Section Description:</label>
                                <textarea id="android_repairs_description" name="android_repairs_description" class="block-input" rows="2">Our dedicated team of repair experts can fix any Android device issue quickly and professionally. From Samsung to Google Pixel, we service all major Android brands.</textarea>
                                <p class="field-help">Describe your Android repair services and brand expertise</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tablet Repair Tab -->
        <div id="tablet-repair-tab" class="tab-content">
            <div class="service-page-editor">
                <div class="page-header">
                    <h2>📱 Tablet Repair Page Editor</h2>
                    <p>Edit content specific to the Tablet repair service page</p>
                    <div class="page-controls">
                        <button type="button" id="load-tablet-content" class="button button-secondary">
                            <span class="dashicons dashicons-download"></span>
                            Load Current Content
                        </button>
                        <button type="button" id="preview-tablet-page" class="button button-secondary">
                            <span class="dashicons dashicons-visibility"></span>
                            Preview Page
                        </button>
                        <button type="button" id="save-tablet-content" class="button button-primary">
                            <span class="dashicons dashicons-yes"></span>
                            Save Changes
                        </button>
                    </div>
                </div>

                <div class="service-content-blocks">
                    <!-- Hero Section -->
                    <div class="content-block">
                        <h3><span class="dashicons dashicons-format-image"></span> Hero Section</h3>
                        <div class="block-fields">
                            <div class="form-row">
                                <label for="tablet_page_title">Page Title:</label>
                                <input type="text" id="tablet_page_title" name="tablet_page_title" class="block-input"
                                       value="Tablet Repairs">
                                <p class="field-help">Main title for the Tablet repair page</p>
                            </div>
                            <div class="form-row">
                                <label for="tablet_hero_description">Hero Description:</label>
                                <textarea id="tablet_hero_description" name="tablet_hero_description" class="block-input" rows="4">Your tablet is useful for traveling, entertaining, and staying productive. When it breaks, you need fast, reliable repair service to get back to what matters most.</textarea>
                                <p class="field-help">Describe why customers should choose you for tablet repairs</p>
                            </div>
                            <div class="form-row">
                                <label for="tablet_cta_button">CTA Button Text:</label>
                                <input type="text" id="tablet_cta_button" name="tablet_cta_button" class="block-input"
                                       value="Select Your Model to Get a Quote">
                                <p class="field-help">Button text for tablet repair quotes</p>
                            </div>
                        </div>
                    </div>

                    <!-- Common Repairs Section -->
                    <div class="content-block">
                        <h3><span class="dashicons dashicons-admin-tools"></span> Common Repairs Section</h3>
                        <div class="block-fields">
                            <div class="form-row">
                                <label for="tablet_repairs_title">Section Title:</label>
                                <input type="text" id="tablet_repairs_title" name="tablet_repairs_title" class="block-input"
                                       value="Tablet Common Repairs">
                                <p class="field-help">Title for the tablet repairs services section</p>
                            </div>
                            <div class="form-row">
                                <label for="tablet_repairs_description">Section Description:</label>
                                <textarea id="tablet_repairs_description" name="tablet_repairs_description" class="block-input" rows="2">Our dedicated team of repair experts can fix any tablet issue quickly and professionally. From iPad to Samsung Galaxy Tab, we service all major tablet brands with quality parts.</textarea>
                                <p class="field-help">Describe your tablet repair services and brand expertise</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Computer Repair Tab -->
        <div id="computer-repair-tab" class="tab-content">
            <div class="service-page-editor">
                <div class="page-header">
                    <h2>💻 Computer Repair Page Editor</h2>
                    <p>Edit content specific to the Computer repair service page</p>
                    <div class="page-controls">
                        <button type="button" id="load-computer-content" class="button button-secondary">
                            <span class="dashicons dashicons-download"></span>
                            Load Current Content
                        </button>
                        <button type="button" id="preview-computer-page" class="button button-secondary">
                            <span class="dashicons dashicons-visibility"></span>
                            Preview Page
                        </button>
                        <button type="button" id="save-computer-content" class="button button-primary">
                            <span class="dashicons dashicons-yes"></span>
                            Save Changes
                        </button>
                    </div>
                </div>

                <div class="service-content-blocks">
                    <!-- Hero Section -->
                    <div class="content-block">
                        <h3><span class="dashicons dashicons-format-image"></span> Hero Section</h3>
                        <div class="block-fields">
                            <div class="form-row">
                                <label for="computer_page_title">Page Title:</label>
                                <input type="text" id="computer_page_title" name="computer_page_title" class="block-input"
                                       value="Computer Repairs">
                                <p class="field-help">Main title for the Computer repair page</p>
                            </div>
                            <div class="form-row">
                                <label for="computer_hero_description">Hero Description:</label>
                                <textarea id="computer_hero_description" name="computer_hero_description" class="block-input" rows="4">Your computer is essential for work and entertainment. When it's not working properly, you need expert repair service to get back to productivity quickly and efficiently.</textarea>
                                <p class="field-help">Describe why customers should choose you for computer repairs</p>
                            </div>
                            <div class="form-row">
                                <label for="computer_cta_button">CTA Button Text:</label>
                                <input type="text" id="computer_cta_button" name="computer_cta_button" class="block-input"
                                       value="Select Your Model to Get a Quote">
                                <p class="field-help">Button text for computer repair quotes</p>
                            </div>
                        </div>
                    </div>

                    <!-- Common Repairs Section -->
                    <div class="content-block">
                        <h3><span class="dashicons dashicons-admin-tools"></span> Common Repairs Section</h3>
                        <div class="block-fields">
                            <div class="form-row">
                                <label for="computer_repairs_title">Section Title:</label>
                                <input type="text" id="computer_repairs_title" name="computer_repairs_title" class="block-input"
                                       value="Computer Common Repairs">
                                <p class="field-help">Title for the computer repairs services section</p>
                            </div>
                            <div class="form-row">
                                <label for="computer_repairs_description">Section Description:</label>
                                <textarea id="computer_repairs_description" name="computer_repairs_description" class="block-input" rows="2">Our dedicated team of repair experts can fix any computer issue quickly and professionally. From hardware failures to software problems, we provide comprehensive computer repair services.</textarea>
                                <p class="field-help">Describe your computer repair services and expertise</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Apple Watch Repair Tab -->
        <div id="watch-repair-tab" class="tab-content">
            <div class="service-page-editor">
                <div class="page-header">
                    <h2>⌚ Apple Watch Repair Page Editor</h2>
                    <p>Edit content specific to the Apple Watch repair service page</p>
                    <div class="page-controls">
                        <button type="button" id="load-watch-content" class="button button-secondary">
                            <span class="dashicons dashicons-download"></span>
                            Load Current Content
                        </button>
                        <button type="button" id="preview-watch-page" class="button button-secondary">
                            <span class="dashicons dashicons-visibility"></span>
                            Preview Page
                        </button>
                        <button type="button" id="save-watch-content" class="button button-primary">
                            <span class="dashicons dashicons-yes"></span>
                            Save Changes
                        </button>
                    </div>
                </div>

                <div class="service-content-blocks">
                    <!-- Hero Section -->
                    <div class="content-block">
                        <h3><span class="dashicons dashicons-format-image"></span> Hero Section</h3>
                        <div class="block-fields">
                            <div class="form-row">
                                <label for="watch_page_title">Page Title:</label>
                                <input type="text" id="watch_page_title" name="watch_page_title" class="block-input"
                                       value="Apple Watch Repairs">
                                <p class="field-help">Main title for the Apple Watch repair page</p>
                            </div>
                            <div class="form-row">
                                <label for="watch_hero_description">Hero Description:</label>
                                <textarea id="watch_hero_description" name="watch_hero_description" class="block-input" rows="4">Your Apple Watch is more than just a timepiece - it's your fitness tracker, communication device, and daily companion. When it needs repair, trust our certified technicians.</textarea>
                                <p class="field-help">Describe why customers should choose you for Apple Watch repairs</p>
                            </div>
                            <div class="form-row">
                                <label for="watch_cta_button">CTA Button Text:</label>
                                <input type="text" id="watch_cta_button" name="watch_cta_button" class="block-input"
                                       value="Select Your Model to Get a Quote">
                                <p class="field-help">Button text for Apple Watch repair quotes</p>
                            </div>
                        </div>
                    </div>

                    <!-- Common Repairs Section -->
                    <div class="content-block">
                        <h3><span class="dashicons dashicons-admin-tools"></span> Common Repairs Section</h3>
                        <div class="block-fields">
                            <div class="form-row">
                                <label for="watch_repairs_title">Section Title:</label>
                                <input type="text" id="watch_repairs_title" name="watch_repairs_title" class="block-input"
                                       value="Apple Watch Common Repairs">
                                <p class="field-help">Title for the Apple Watch repairs services section</p>
                            </div>
                            <div class="form-row">
                                <label for="watch_repairs_description">Section Description:</label>
                                <textarea id="watch_repairs_description" name="watch_repairs_description" class="block-input" rows="2">Our dedicated team of repair experts can fix any Apple Watch issue quickly and professionally. From cracked screens to battery replacements, we service all Apple Watch models.</textarea>
                                <p class="field-help">Describe your Apple Watch repair services and expertise</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Console Repair Tab -->
        <div id="console-repair-tab" class="tab-content">
            <div class="service-page-editor">
                <div class="page-header">
                    <h2>🎮 Game Console Repair Page Editor</h2>
                    <p>Edit content specific to the Game Console repair service page</p>
                    <div class="page-controls">
                        <button type="button" id="load-console-content" class="button button-secondary">
                            <span class="dashicons dashicons-download"></span>
                            Load Current Content
                        </button>
                        <button type="button" id="preview-console-page" class="button button-secondary">
                            <span class="dashicons dashicons-visibility"></span>
                            Preview Page
                        </button>
                        <button type="button" id="save-console-content" class="button button-primary">
                            <span class="dashicons dashicons-yes"></span>
                            Save Changes
                        </button>
                    </div>
                </div>

                <div class="service-content-blocks">
                    <!-- Hero Section -->
                    <div class="content-block">
                        <h3><span class="dashicons dashicons-format-image"></span> Hero Section</h3>
                        <div class="block-fields">
                            <div class="form-row">
                                <label for="console_page_title">Page Title:</label>
                                <input type="text" id="console_page_title" name="console_page_title" class="block-input"
                                       value="Game Console Repairs">
                                <p class="field-help">Main title for the Game Console repair page</p>
                            </div>
                            <div class="form-row">
                                <label for="console_hero_description">Hero Description:</label>
                                <textarea id="console_hero_description" name="console_hero_description" class="block-input" rows="4">Your gaming console is your gateway to entertainment and competition. When it's not working properly, you need expert repair service to get back to gaming quickly.</textarea>
                                <p class="field-help">Describe why customers should choose you for game console repairs</p>
                            </div>
                            <div class="form-row">
                                <label for="console_cta_button">CTA Button Text:</label>
                                <input type="text" id="console_cta_button" name="console_cta_button" class="block-input"
                                       value="Select Your Model to Get a Quote">
                                <p class="field-help">Button text for game console repair quotes</p>
                            </div>
                        </div>
                    </div>

                    <!-- Common Repairs Section -->
                    <div class="content-block">
                        <h3><span class="dashicons dashicons-admin-tools"></span> Common Repairs Section</h3>
                        <div class="block-fields">
                            <div class="form-row">
                                <label for="console_repairs_title">Section Title:</label>
                                <input type="text" id="console_repairs_title" name="console_repairs_title" class="block-input"
                                       value="Game Console Common Repairs">
                                <p class="field-help">Title for the game console repairs services section</p>
                            </div>
                            <div class="form-row">
                                <label for="console_repairs_description">Section Description:</label>
                                <textarea id="console_repairs_description" name="console_repairs_description" class="block-input" rows="2">Our dedicated team of repair experts can fix any gaming console issue quickly and professionally. From PlayStation to Xbox to Nintendo Switch, we service all major gaming platforms.</textarea>
                                <p class="field-help">Describe your game console repair services and platform expertise</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Global Settings Tab -->
        <div id="global-settings-tab" class="tab-content">
            <div class="global-settings-editor">
                <div class="page-header">
                    <h2>🌐 Global Settings</h2>
                    <p>Manage content that appears across all service pages</p>
                    <div class="page-controls">
                        <button type="button" id="load-global-content" class="button button-secondary">
                            <span class="dashicons dashicons-download"></span>
                            Load Current Settings
                        </button>
                        <button type="button" id="save-global-content" class="button button-primary">
                            <span class="dashicons dashicons-yes"></span>
                            Save Global Changes
                        </button>
                    </div>
                </div>

                <div class="global-content-blocks">
                    <!-- Customer Benefits Section -->
                    <div class="content-block">
                        <h3><span class="dashicons dashicons-star-filled"></span> Customer Benefits (Shared Across All Pages)</h3>
                        <div class="benefits-grid">
                            <div class="benefit-item">
                                <h4>Benefit 1</h4>
                                <div class="form-row">
                                    <label for="benefit1_title">Title:</label>
                                    <input type="text" id="benefit1_title" name="benefit1_title" class="block-input"
                                           value="PREMIER CUSTOMER SERVICE">
                                    <p class="field-help">First customer benefit title (appears on all service pages)</p>
                                </div>
                                <div class="form-row">
                                    <label for="benefit1_description">Description:</label>
                                    <textarea id="benefit1_description" name="benefit1_description" class="block-input" rows="3">With us, you are guaranteed a professional, original quality repair with a fast turnaround time and excellent customer service.</textarea>
                                    <p class="field-help">Describe your premier customer service commitment</p>
                                </div>
                            </div>

                            <div class="benefit-item">
                                <h4>Benefit 2</h4>
                                <div class="form-row">
                                    <label for="benefit2_title">Title:</label>
                                    <input type="text" id="benefit2_title" name="benefit2_title" class="block-input"
                                           value="QUICK TURNAROUND">
                                    <p class="field-help">Second customer benefit title (appears on all service pages)</p>
                                </div>
                                <div class="form-row">
                                    <label for="benefit2_description">Description:</label>
                                    <textarea id="benefit2_description" name="benefit2_description" class="block-input" rows="3">We strive to get your device back to its original condition as quickly as possible, often with same-day or next-day service available.</textarea>
                                    <p class="field-help">Describe your quick turnaround time commitment</p>
                                </div>
                            </div>

                            <div class="benefit-item">
                                <h4>Benefit 3</h4>
                                <div class="form-row">
                                    <label for="benefit3_title">Title:</label>
                                    <input type="text" id="benefit3_title" name="benefit3_title" class="block-input"
                                           value="LOW PRICE GUARANTEE">
                                    <p class="field-help">Third customer benefit title (appears on all service pages)</p>
                                </div>
                                <div class="form-row">
                                    <label for="benefit3_description">Description:</label>
                                    <textarea id="benefit3_description" name="benefit3_description" class="block-input" rows="3">We know price is a big factor in your decision. That's why we offer competitive pricing with a low price guarantee on all our repair services.</textarea>
                                    <p class="field-help">Describe your competitive pricing and guarantees</p>
                                </div>
                            </div>

                            <div class="benefit-item">
                                <h4>Benefit 4</h4>
                                <div class="form-row">
                                    <label for="benefit4_title">Title:</label>
                                    <input type="text" id="benefit4_title" name="benefit4_title" class="block-input"
                                           value="EXPERT TECHNICIANS">
                                    <p class="field-help">Fourth customer benefit title (appears on all service pages)</p>
                                </div>
                                <div class="form-row">
                                    <label for="benefit4_description">Description:</label>
                                    <textarea id="benefit4_description" name="benefit4_description" class="block-input" rows="3">Our technicians love what they do. They have been servicing devices for years and stay up-to-date with the latest repair techniques and technologies.</textarea>
                                    <p class="field-help">Describe your technician expertise and experience</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media Links -->
                    <div class="content-block">
                        <h3><span class="dashicons dashicons-share"></span> Social Media Links</h3>
                        <div class="block-fields">
                            <div class="form-row">
                                <label for="facebook_url">Facebook URL:</label>
                                <input type="url" id="facebook_url" name="facebook_url" class="block-input"
                                       placeholder="https://facebook.com/yourpage">
                                <p class="description">Currently shows as "#" placeholder on all pages</p>
                            </div>
                            <div class="form-row">
                                <label for="instagram_url">Instagram URL:</label>
                                <input type="url" id="instagram_url" name="instagram_url" class="block-input"
                                       placeholder="https://instagram.com/yourpage">
                                <p class="description">Currently shows as "#" placeholder on all pages</p>
                            </div>
                        </div>
                    </div>

                    <!-- Footer Content -->
                    <div class="content-block">
                        <h3><span class="dashicons dashicons-admin-page"></span> Footer Content</h3>
                        <div class="block-fields">
                            <div class="form-row">
                                <label for="footer_copyright">Copyright Text:</label>
                                <textarea id="footer_copyright" name="footer_copyright" class="block-input" rows="2"
                                          placeholder="© 2023 All Rights Reserved | Not affiliated with Apple, Inc., LG Corp. or Samsung Corp"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Setup Tab -->
        <div id="quick-setup-tab" class="tab-content">
            <div class="generator-form-section">
                <h2>Quick Setup</h2>
                <p>Fill out basic information to quickly update your website</p>
                <form id="quick-setup-form" enctype="multipart/form-data">
                    <?php wp_nonce_field('website_generator_nonce', 'website_generator_nonce'); ?>

                    <table class="form-table">
                        <tbody>
                            <tr>
                                <th scope="row">
                                    <label for="company_name">Company Name *</label>
                                </th>
                                <td>
                                    <input type="text" id="company_name" name="company_name" class="regular-text" required
                                           value="RepairLift Device Repair">
                                    <p class="description">This will be the site title and main business name</p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row">
                                    <label for="tagline">Tagline</label>
                                </th>
                                <td>
                                    <input type="text" id="tagline" name="tagline" class="regular-text"
                                           value="Professional Device Repair Services">
                                    <p class="description">Short description that appears with your business name</p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row">
                                    <label for="phone">Phone Number</label>
                                </th>
                                <td>
                                    <input type="tel" id="phone" name="phone" class="regular-text"
                                           value="(*************">
                                    <p class="description">Your main business phone number</p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row">
                                    <label for="address">Business Address</label>
                                </th>
                                <td>
                                    <textarea id="address" name="address" rows="3" cols="50">123 Main Street
Anytown, FL 12345</textarea>
                                    <p class="description">Your complete business address</p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row">
                                    <label for="primary_color">Primary Color</label>
                                </th>
                                <td>
                                    <input type="color" id="primary_color" name="primary_color" value="#9DFF20">
                                    <span class="color-preview" id="primary-preview" style="display: inline-block; width: 30px; height: 30px; background: #9DFF20; margin-left: 10px; border: 1px solid #ccc;"></span>
                                    <p class="description">Main brand color used for buttons and links</p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row">
                                    <label for="secondary_color">Secondary Color</label>
                                </th>
                                <td>
                                    <input type="color" id="secondary_color" name="secondary_color" value="#345C00">
                                    <span class="color-preview" id="secondary-preview" style="display: inline-block; width: 30px; height: 30px; background: #345C00; margin-left: 10px; border: 1px solid #ccc;"></span>
                                    <p class="description">Secondary brand color for accents and highlights</p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row">
                                    <label for="accent_color">Accent Color</label>
                                </th>
                                <td>
                                    <input type="color" id="accent_color" name="accent_color" value="#165C9C">
                                    <span class="color-preview" id="accent-preview" style="display: inline-block; width: 30px; height: 30px; background: #165C9C; margin-left: 10px; border: 1px solid #ccc;"></span>
                                    <p class="description">Accent color for special elements and highlights</p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row">
                                    <label for="logo">Company Logo</label>
                                </th>
                                <td>
                                    <input type="file" id="logo" name="logo" accept="image/*">
                                    <div id="logo-preview" style="margin-top: 10px;"></div>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <p class="submit">
                        <button type="submit" class="button button-primary button-large" id="quick-generate">
                            <span class="dashicons dashicons-admin-site-alt3"></span>
                            Quick Generate Website
                        </button>
                        <button type="button" id="create-backup-btn" class="button button-secondary">
                            <span class="dashicons dashicons-backup"></span>
                            Create Backup
                        </button>
                    </p>
                </form>
            </div>
        </div>

        <!-- Advanced Tools Tab -->
        <div id="advanced-tools-tab" class="tab-content">

            <!-- Backup Management Section -->
            <div class="management-section">
                <h3><span class="dashicons dashicons-backup"></span> Backup Management</h3>
                <p>Save and restore website configurations for safe experimentation</p>

                <div class="section-actions">
                    <button type="button" id="create-backup-btn" class="button button-secondary">
                        <span class="dashicons dashicons-backup"></span>
                        Create Backup
                    </button>
                    <button type="button" id="load-backups-btn" class="button button-secondary">
                        <span class="dashicons dashicons-download"></span>
                        Load Backups
                    </button>
                </div>

                <div id="backup-results" class="results-container" style="display: none;">
                    <h4>Available Backups</h4>
                    <div id="backup-list"></div>
                </div>
            </div>

            <!-- Text Content Editor Section -->
            <div class="management-section">
                <h3><span class="dashicons dashicons-edit"></span> Text Content Editor</h3>
                <p>Find and edit ALL text content visible on the website</p>

                <div class="section-actions">
                    <button type="button" id="load-text-content-btn" class="button button-secondary">
                        <span class="dashicons dashicons-search"></span>
                        Load Text Content
                    </button>
                    <button type="button" id="save-text-content-btn" class="button button-primary" style="display: none;">
                        <span class="dashicons dashicons-yes"></span>
                        Save Text Changes
                    </button>
                </div>

                <div id="text-content-results" class="results-container" style="display: none;">
                    <h4>Editable Text Content</h4>
                    <div id="text-content-list"></div>
                </div>
            </div>

            <!-- Site Cleanup Section -->
            <div class="management-section">
                <h3><span class="dashicons dashicons-admin-tools"></span> Site Cleanup</h3>
                <p>Identify unused plugins/themes for removal and optimization</p>

                <div class="section-actions">
                    <button type="button" id="scan-unused-assets-btn" class="button button-secondary">
                        <span class="dashicons dashicons-search"></span>
                        Scan for Unused Assets
                    </button>
                </div>

                <div id="cleanup-results" class="results-container" style="display: none;">
                    <h4>Cleanup Recommendations</h4>
                    <div id="cleanup-list"></div>
                </div>
            </div>

        </div>

        <!-- Loading Overlay -->
        <div id="loading-overlay" style="display: none;">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p>Processing...</p>
            </div>
        </div>
    </div>
    
    <!-- Results Section -->
    <div id="generation-results" style="display: none; margin-top: 30px;">
        <h3>Generation Results</h3>
        <div id="results-content"></div>
    </div>
</div>

<style>
/* Tab Navigation */
.nav-tab-wrapper {
    margin-bottom: 20px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Visual Block Editor Styles */
.visual-editor-header {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    margin-bottom: 20px;
    border-radius: 4px;
}

.editor-controls {
    margin-top: 15px;
}

.editor-controls .button {
    margin-right: 10px;
}

.visual-blocks-container {
    display: grid;
    gap: 20px;
}

.visual-block {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.block-header {
    background: #f1f1f1;
    padding: 15px 20px;
    border-bottom: 1px solid #ccd0d4;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.block-header h3 {
    margin: 0;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-indicator {
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
}

.status-indicator[data-status="unchanged"] {
    background: #e5e5e5;
    color: #666;
}

.status-indicator[data-status="modified"] {
    background: #fff3cd;
    color: #856404;
}

.status-indicator[data-status="saved"] {
    background: #d4edda;
    color: #155724;
}

.block-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    padding: 20px;
}

.block-editor {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-row {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.form-row label {
    font-weight: 600;
    color: #23282d;
}

.block-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.block-input:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 1px #0073aa;
}

.block-preview {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
}

.preview-container {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.hero-preview h1 {
    font-size: 32px;
    margin: 0 0 10px 0;
    color: #333;
}

.hero-preview p {
    font-size: 18px;
    margin: 0 0 20px 0;
    color: #666;
}

.preview-buttons {
    display: flex;
    gap: 10px;
}

.preview-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
}

/* WordPress Theme Color Integration */
.preview-btn.primary {
    background: var(--wp--preset--color--primary, #9DFF20);
    color: var(--wp--preset--color--contrast, #000000);
}

.preview-btn.secondary {
    background: var(--wp--preset--color--secondary, #345C00);
    color: var(--wp--preset--color--base, #ffffff);
}

.preview-btn.accent {
    background: var(--wp--preset--color--tertiary, #165C9C);
    color: var(--wp--preset--color--contrast, #000000);
}

.preview-btn.outline-primary {
    background: transparent;
    color: var(--wp--preset--color--primary, #9DFF20);
    border: 2px solid var(--wp--preset--color--primary, #9DFF20);
}

.preview-btn.outline-secondary {
    background: transparent;
    color: var(--wp--preset--color--secondary, #345C00);
    border: 2px solid var(--wp--preset--color--secondary, #345C00);
}

.preview-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Design System Styles */
.form-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.color-input-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.color-input {
    width: 50px !important;
    height: 40px !important;
    padding: 0 !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    cursor: pointer;
}

.color-value {
    font-family: monospace;
    font-size: 12px;
    color: #666;
    background: #f5f5f5;
    padding: 4px 8px;
    border-radius: 3px;
    min-width: 70px;
    text-align: center;
}

/* Enhanced Color Swatches */
.color-swatches {
    display: flex;
    gap: 30px;
    margin: 20px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
}

.color-swatch {
    text-align: center;
    flex: 1;
}

.swatch {
    width: 80px;
    height: 80px;
    border-radius: 12px;
    border: 3px solid #fff;
    margin: 0 auto 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s, box-shadow 0.2s;
    cursor: pointer;
}

.swatch:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.color-swatch span {
    display: block;
    font-weight: 600;
    color: #23282d;
    margin-bottom: 4px;
    font-size: 14px;
}

.color-hex {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #666;
    background: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #ddd;
    display: inline-block;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.color-hex:hover {
    background: #f0f0f0;
    border-color: #999;
    transform: translateY(-1px);
}

.color-hex.copied {
    background: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

/* Enhanced Color Input Groups */
.color-input-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.color-input {
    width: 60px;
    height: 40px;
    border: 2px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    transition: border-color 0.2s;
}

.color-input:hover {
    border-color: #0073aa;
}

.color-value {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
    min-width: 70px;
}

/* Button Preview Layout */
.button-preview {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
}

.button-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.button-row:last-child {
    margin-bottom: 0;
}

.button-preview .preview-btn {
    flex: 1;
    min-width: 120px;
}

/* Template Preview Styles */
.template-preview {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #e1e5e9;
}

.preview-section {
    background: white;
    padding: 20px;
    margin-bottom: 15px;
    border-radius: 6px;
    border: 1px solid #e1e5e9;
    text-align: center;
}

.preview-section:last-child {
    margin-bottom: 0;
}

.preview-section.hero-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.preview-section.about-section {
    background: var(--wp--preset--color--tertiary, #F6F6F6);
}

.preview-section .preview-heading {
    margin: 0 0 10px 0;
    color: var(--wp--preset--color--contrast, #000000);
}

.preview-section .preview-tagline {
    margin: 0 0 15px 0;
    color: #666;
    font-size: 14px;
}

.preview-section .preview-body {
    margin: 0 0 15px 0;
    color: #555;
    line-height: 1.5;
}

.preview-section .preview-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Button States Preview */
.button-states-preview {
    display: flex;
    gap: 20px;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.button-state {
    display: flex;
    align-items: center;
    gap: 10px;
}

.state-label {
    font-size: 12px;
    color: #666;
    font-weight: 600;
    min-width: 50px;
}

.preview-btn.hover-state {
    background: var(--wp--preset--color--contrast, #000000) !important;
    color: var(--wp--preset--color--base, #ffffff) !important;
}

/* Section Description Styling */
.section-description {
    color: #666;
    font-size: 14px;
    margin: 0 0 20px 0;
    font-style: italic;
}

/* Integrated Color Design Container */
.color-design-container {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.color-controls {
    flex: 1;
    min-width: 300px;
}

.live-preview {
    flex: 1;
    min-width: 350px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e1e5e9;
    position: sticky;
    top: 20px;
}

.live-preview h5 {
    margin: 0 0 15px 0;
    color: #23282d;
    font-size: 16px;
    font-weight: 600;
}

/* Enhanced Color Input Groups */
.color-controls .color-input-group {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 10px;
}

.color-controls .color-input-group .color-swatch {
    margin-left: auto;
}

.color-controls .color-swatch .swatch {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: transform 0.2s;
}

.color-controls .color-swatch .swatch:hover {
    transform: scale(1.1);
}

/* Compact Template Preview */
.live-preview .template-preview {
    background: white;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid #e1e5e9;
}

.live-preview .preview-section {
    background: transparent;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 4px;
    text-align: center;
    border: 1px solid #f0f0f0;
}

.live-preview .preview-section:last-child {
    margin-bottom: 0;
}

.live-preview .preview-section.about-section {
    background: var(--wp--preset--color--tertiary, #F6F6F6);
}

.live-preview .preview-heading {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: var(--wp--preset--color--contrast, #000000);
}

.live-preview .preview-tagline {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 12px;
}

.live-preview .preview-body {
    margin: 0 0 10px 0;
    color: #555;
    font-size: 13px;
    line-height: 1.4;
}

.live-preview .preview-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
    flex-wrap: wrap;
}

.live-preview .preview-btn {
    padding: 8px 16px;
    font-size: 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-weight: 600;
}

.live-preview .preview-btn.hover-demo {
    background: var(--wp--preset--color--contrast, #000000) !important;
    color: var(--wp--preset--color--base, #ffffff) !important;
}

/* Color Legend */
.color-legend {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e1e5e9;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #666;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    border: 1px solid #ddd;
}

.legend-color.primary-demo {
    background: var(--wp--preset--color--primary, #9DFF20);
}

.legend-color.secondary-demo {
    background: var(--wp--preset--color--secondary, #345C00);
}

.legend-color.background-demo {
    background: var(--wp--preset--color--tertiary, #F6F6F6);
}

.legend-color.primary-text-demo {
    background: var(--primary-text-color, #FFFFFF);
    border: 1px solid #ddd;
}

.legend-color.secondary-text-demo {
    background: var(--secondary-text-color, #FFFFFF);
    border: 1px solid #ddd;
}

/* Button Text Color Styling */
.live-preview .preview-btn.primary {
    background: var(--wp--preset--color--primary, #9DFF20) !important;
    color: var(--primary-text-color, #FFFFFF) !important;
    border: none;
}

.live-preview .preview-btn.secondary {
    background: var(--wp--preset--color--secondary, #345C00) !important;
    color: var(--secondary-text-color, #FFFFFF) !important;
    border: none;
}

/* Enhanced Color Input for Text Colors */
.color-controls .color-input-group .swatch[id*="text-color"] {
    border: 2px solid #ddd !important;
}

/* Button Text Color Variables */
:root {
    --primary-text-color: #FFFFFF;
    --secondary-text-color: #FFFFFF;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .color-design-container {
        flex-direction: column;
    }

    .live-preview {
        position: static;
    }
}
    color: #666;
    font-weight: 500;
}

.button-preview {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.design-preview h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
}

/* Button Style Variations */
.preview-btn.pill {
    border-radius: 65px;
}

.preview-btn.rounded {
    border-radius: 12px;
}

.preview-btn.square {
    border-radius: 0px;
}

.preview-btn.small {
    padding: 8px 20px;
    font-size: 14px;
}

.preview-btn.medium {
    padding: 12px 24px;
    font-size: 16px;
}

.preview-btn.large {
    padding: 16px 32px;
    font-size: 18px;
}

.description {
    font-size: 12px;
    color: #666;
    margin: 5px 0 0 0;
    font-style: italic;
}

/* Logo Editing Styles */
.logo-upload-container {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: border-color 0.3s;
}

.logo-upload-container:hover {
    border-color: #0073aa;
}

.logo-upload-container.dragover {
    border-color: #0073aa;
    background-color: #f0f8ff;
}

.upload-help {
    margin-top: 10px;
}

.upload-help p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.editing-toolbar {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.edit-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: #f7f7f7;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.2s;
}

.edit-btn:hover {
    background: #0073aa;
    color: white;
    border-color: #0073aa;
}

.edit-btn.active {
    background: #0073aa;
    color: white;
    border-color: #0073aa;
}

.editing-panel {
    margin-top: 15px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.editing-panel h5 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
}

.crop-controls, .resize-controls, .adjustment-controls, .filter-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.size-inputs {
    display: grid;
    grid-template-columns: auto 1fr auto 1fr;
    gap: 10px;
    align-items: center;
}

.size-inputs input[type="number"] {
    width: 80px;
}

.size-presets {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.preset-btn {
    padding: 6px 12px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s;
}

.preset-btn:hover {
    background: #f0f0f0;
    border-color: #0073aa;
}

.slider-group {
    display: grid;
    grid-template-columns: 100px 1fr 50px;
    gap: 10px;
    align-items: center;
}

.adjustment-slider {
    width: 100%;
}

.slider-value {
    font-size: 12px;
    color: #666;
    text-align: center;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 10px;
}

.filter-btn {
    padding: 10px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
}

.filter-btn:hover {
    background: #f0f0f0;
    border-color: #0073aa;
}

.filter-btn.active {
    background: #0073aa;
    color: white;
    border-color: #0073aa;
}

.apply-btn {
    padding: 10px 20px;
    background: #0073aa;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: background 0.2s;
}

.apply-btn:hover {
    background: #005a87;
}

.reset-btn {
    padding: 10px 20px;
    background: #666;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-left: 10px;
    transition: background 0.2s;
}

.reset-btn:hover {
    background: #444;
}

.logo-preview-area {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.preview-logo-container {
    position: relative;
    min-height: 200px;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f9f9f9;
}

.logo-placeholder {
    text-align: center;
    color: #666;
}

.logo-placeholder .dashicons {
    font-size: 48px;
    margin-bottom: 10px;
    opacity: 0.5;
}

.logo-placeholder p {
    margin: 0;
    font-size: 14px;
}

#preview-logo {
    max-width: 100%;
    max-height: 200px;
    object-fit: contain;
}

.logo-info {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
}

.logo-info p {
    margin: 5px 0;
}

#logo-edit-canvas {
    margin-top: 15px;
    border-radius: 4px;
}

/* Service Page Editor Styles */
.service-page-editor, .global-settings-editor {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    overflow: hidden;
}

.page-header {
    background: #f1f1f1;
    padding: 20px;
    border-bottom: 1px solid #ccd0d4;
}

.page-header h2 {
    margin: 0 0 10px 0;
    font-size: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-header p {
    margin: 0 0 15px 0;
    color: #666;
}

.page-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.service-content-blocks, .global-content-blocks {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.content-block {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
}

.content-block h3 {
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: 600;
    color: #23282d;
    display: flex;
    align-items: center;
    gap: 8px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.block-fields {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.block-fields .form-row {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.block-fields label {
    font-weight: 600;
    color: #23282d;
    font-size: 14px;
}

.block-fields .block-input {
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
    background: white;
}

.block-fields .block-input:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 1px #0073aa;
}

/* Field Help Text */
.field-help {
    font-size: 12px;
    color: #666;
    margin: 5px 0 0 0;
    font-style: italic;
    line-height: 1.4;
}

/* Enhanced Form Styling */
.form-row {
    margin-bottom: 20px;
}

.form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #23282d;
}

.block-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
    background: white;
    font-family: inherit;
}

.block-input:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 1px #0073aa;
}

/* Textarea specific styling */
textarea.block-input {
    resize: vertical;
    min-height: 80px;
    line-height: 1.5;
}

/* Content State Indicators */
.block-input.has-content {
    border-left: 3px solid #46b450;
    background: #f9fff9;
}

.block-input.is-empty {
    border-left: 3px solid #ddd;
    background: #fafafa;
}

.block-input.is-focused {
    background: #fff;
    border-color: #0073aa;
}

/* Enhanced field help styling */
.field-help {
    font-size: 12px;
    color: #666;
    margin: 5px 0 0 0;
    font-style: italic;
    line-height: 1.4;
    padding: 5px 10px;
    background: #f8f9fa;
    border-radius: 3px;
    border-left: 3px solid #0073aa;
}

/* Content status indicators */
.content-status {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    margin-left: 10px;
}

.content-status.loaded {
    background: #d4edda;
    color: #155724;
}

.content-status.modified {
    background: #fff3cd;
    color: #856404;
}

.content-status.saved {
    background: #cce5ff;
    color: #004085;
}

/* Global Settings Specific Styles */
.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.benefit-item {
    background: white;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.benefit-item h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
    color: #0073aa;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Tab Navigation Enhancement */
.nav-tab-wrapper .nav-tab {
    font-size: 13px;
    padding: 8px 12px;
}

.nav-tab-wrapper .nav-tab:hover {
    background-color: #f0f0f0;
}

/* Enhanced Design System Styles */

/* Typography Preview */
.typography-preview {
    background: white;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 20px;
}

.preview-heading {
    margin: 0 0 10px 0;
    color: #333;
    transition: all 0.3s ease;
}

.preview-body {
    margin: 0;
    color: #666;
    transition: all 0.3s ease;
}

/* Font Family Classes */
.font-modern {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.font-classic .preview-heading {
    font-family: 'Playfair Display', Georgia, serif;
}
.font-classic .preview-body {
    font-family: 'Source Sans Pro', Arial, sans-serif;
}

.font-tech .preview-heading {
    font-family: 'Space Grotesk', 'Inter', sans-serif;
}
.font-tech .preview-body {
    font-family: 'DM Sans', 'Inter', sans-serif;
}

.font-friendly .preview-heading {
    font-family: 'Poppins', 'Inter', sans-serif;
}
.font-friendly .preview-body {
    font-family: 'Open Sans', Arial, sans-serif;
}

/* Heading Sizes */
.heading-small .preview-heading { font-size: 24px; }
.heading-medium .preview-heading { font-size: 32px; }
.heading-large .preview-heading { font-size: 42px; }

/* Body Sizes */
.body-14 .preview-body { font-size: 14px; }
.body-16 .preview-body { font-size: 16px; }
.body-18 .preview-body { font-size: 18px; }

/* Button Shadow Effects */
.shadow-none { box-shadow: none; }
.shadow-subtle { box-shadow: 0 1px 2px rgba(0,0,0,0.05); }
.shadow-medium { box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
.shadow-strong { box-shadow: 0 10px 15px rgba(0,0,0,0.1); }

/* Button Hover Effects */
.hover-lift {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}
.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.hover-scale {
    transition: transform 0.2s ease;
}
.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: box-shadow 0.3s ease;
}
.hover-glow:hover {
    box-shadow: 0 0 20px rgba(22,92,156,0.4);
}

.hover-slide {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}
.hover-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}
.hover-slide:hover::before {
    left: 100%;
}

/* Animation Preview */
.animation-preview {
    background: #f5f5f5;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-top: 20px;
}

.preview-hero {
    background: white;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.preview-hero h2 {
    margin: 0 0 10px 0;
    color: #333;
}

.preview-hero p {
    margin: 0 0 20px 0;
    color: #666;
}

.preview-image-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
}

.preview-image {
    font-size: 48px;
    margin-bottom: 10px;
    display: block;
}

.replay-animation-btn {
    background: #0073aa;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    margin: 0 auto;
    transition: background 0.2s ease;
}

.replay-animation-btn:hover {
    background: #005a87;
}

/* Animation Keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-10deg) scale(0.9);
    }
    to {
        opacity: 1;
        transform: rotate(0deg) scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Animation Classes */
.animate-fadeInUp { animation: fadeInUp 0.6s ease forwards; }
.animate-fadeInLeft { animation: fadeInLeft 0.6s ease forwards; }
.animate-scaleIn { animation: scaleIn 0.6s ease forwards; }
.animate-slideInRight { animation: slideInRight 0.6s ease forwards; }
.animate-fadeIn { animation: fadeIn 0.6s ease forwards; }
.animate-rotateIn { animation: rotateIn 0.6s ease forwards; }
.animate-pulse { animation: pulse 2s ease-in-out infinite; }

/* Animation Speed Modifiers */
.animate-fast { animation-duration: 0.3s !important; }
.animate-normal { animation-duration: 0.6s !important; }
.animate-slow { animation-duration: 1s !important; }

/* Animation Delay Modifiers */
.animate-delay-short { animation-delay: 0.2s !important; }
.animate-delay-medium { animation-delay: 0.5s !important; }
.animate-delay-long { animation-delay: 1s !important; }

/* Responsive Design for Service Pages */
@media (max-width: 768px) {
    .page-controls {
        flex-direction: column;
    }

    .page-controls .button {
        width: 100%;
        justify-content: center;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
    }

    .nav-tab-wrapper {
        overflow-x: auto;
        white-space: nowrap;
    }

    .nav-tab-wrapper .nav-tab {
        display: inline-block;
        min-width: 120px;
        text-align: center;
    }

    .preview-hero {
        padding: 20px;
    }

    .preview-image {
        font-size: 36px;
    }

    .typography-preview {
        padding: 15px;
    }
}

/* Upload Progress and Results Styles */
.upload-progress {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
}

.progress-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.upload-results {
    background: #f0f8ff;
    border: 1px solid #0073aa;
    border-radius: 6px;
    padding: 15px;
    margin-top: 15px;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.upload-results h4 {
    margin: 0 0 10px 0;
    color: #0073aa;
    font-size: 16px;
}

.upload-results .success {
    color: #46b450;
    font-weight: 600;
    margin: 5px 0;
}

.upload-results .info {
    color: #666;
    margin: 5px 0;
    font-size: 14px;
}

.webp-results {
    background: #e8f5e8;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
}

.size-comparison {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;
    color: #666;
}

.size-comparison span {
    background: white;
    padding: 4px 8px;
    border-radius: 3px;
    border: 1px solid #ddd;
}

/* Fix Button Icon Alignment */
.button {
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    vertical-align: middle !important;
    line-height: 1.4 !important;
}

.button .dashicons {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 18px !important;
    height: 18px !important;
    font-size: 18px !important;
    line-height: 1 !important;
    vertical-align: middle !important;
    margin: 0 !important;
    flex-shrink: 0 !important;
}

/* Editor controls specific styling */
.editor-controls .button,
.page-controls .button {
    padding: 8px 16px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    border-radius: 4px !important;
}

.editor-controls {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 20px;
}

.page-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    align-items: center;
}

/* Ensure proper button text alignment */
.button-text {
    display: inline-block;
    vertical-align: middle;
    line-height: 1.4;
}

/* Management Sections */
.management-section {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    margin-bottom: 20px;
    border-radius: 4px;
}

.section-actions {
    margin-top: 15px;
}

.section-actions .button {
    margin-right: 10px;
}

.results-container {
    margin-top: 20px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Form Styles */
.generator-form-section {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    border-radius: 4px;
}

/* Loading Overlay */
#loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0073aa;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .block-content {
        grid-template-columns: 1fr;
    }

    .preview-buttons {
        flex-direction: column;
    }

    .editor-controls .button {
        margin-bottom: 10px;
    }
}

.color-preview {
    border-radius: 3px;
    vertical-align: middle;
}

#logo-preview img {
    max-width: 200px;
    max-height: 100px;
    border: 1px solid #ddd;
    padding: 5px;
    background: #fff;
}

.form-table th {
    width: 200px;
}

.button-large {
    height: auto !important;
    line-height: 1.4 !important;
    padding: 12px 20px !important;
    font-size: 14px !important;
}

.button .dashicons {
    margin-right: 5px;
    margin-top: -2px;
}

#generation-results {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.success-message {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 12px;
    border-radius: 4px;
    margin: 10px 0;
}

.error-message {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 12px;
    border-radius: 4px;
    margin: 10px 0;
}
</style>
